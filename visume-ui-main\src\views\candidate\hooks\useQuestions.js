import { useState, useEffect, useCallback, useRef } from "react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

// Default questions for fallback
const getDefaultQuestion = (role, skill, type = 'technical') => {
  const questions = {
    technical: {
      question: `Describe a challenging problem you've solved using ${skill}. What was your approach and solution?`,
      type: 'technical'
    },
    behavioral: {
      question: `How do you handle difficult situations in your role as a ${role}? Give a specific example.`,
      type: 'behavioral'
    }
  };
  return questions[type] || questions.technical;
};

export function useQuestions(isInterviewActive = false) {
  // Initialize state from localStorage if available
  const [allQuestions, setAllQuestions] = useState(() => {
    const saved = localStorage.getItem("questions");
    return saved ? JSON.parse(saved) : [];
  });

  const [currentIndex, setCurrentIndex] = useState(0); // Always start with first question

  const [currentQuestion, setCurrentQuestion] = useState(() => {
    try {
      const saved = localStorage.getItem("questions");
      const questions = saved ? JSON.parse(saved) : [];
      const index = parseInt(localStorage.getItem("currentQuestionIndex"), 10) || 0;

      // Ensure we have a valid question
      if (questions && questions.length > 0 && index >= 0 && index < questions.length) {
        return questions[index];
      }

      // Return null if no questions loaded yet (instead of default)
      return null;
    } catch (error) {
      console.error("Error initializing current question:", error);
      return null;
    }
  });

  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false); // Don't auto-load
  const [questionsLoaded, setQuestionsLoaded] = useState(false);

  // Keep currentQuestion in sync with questions and index
  // Keep currentQuestion in sync and ensure it's always valid
  // Initialize isInitialMount ref outside the effect
  const isInitialMount = useRef(true);

  useEffect(() => {
    // Skip validation on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Only validate after questions are initialized
    if (allQuestions.length > 0 && currentIndex >= 0 && currentIndex < allQuestions.length) {
      const question = allQuestions[currentIndex];
      if (question && JSON.stringify(question) !== JSON.stringify(currentQuestion)) {
        setCurrentQuestion(question);
      }
      localStorage.setItem("questions", JSON.stringify(allQuestions));
      localStorage.setItem("currentQuestionIndex", currentIndex.toString());
    } else if (!isInitialMount.current && !isLoading) {
      // Only warn if not in initial mount or loading state
      console.warn("Invalid questions state:", {
        questionsLength: allQuestions.length,
        currentIndex,
        isLoading,
        currentQuestion: currentQuestion?.question || null
      });
    }
  }, [allQuestions, currentIndex, currentQuestion]);

  const loadQuestionsData = async () => {
    try {
      setIsLoading(true);
      console.log('📚 Loading questions data (not activating yet)');

      // Get and validate cookie data with retries
      let createVRResJson;
      const maxRetries = 3;
      const retryDelay = 500; // 500ms

      for (let i = 0; i < maxRetries; i++) {
        createVRResJson = Cookies.get("CreateVRres");
        const allCookies = Cookies.get();

        if (createVRResJson) break;

        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      if (!createVRResJson) {
        const allCookies = Cookies.get();
        const error = "Interview data not found after retries";
        console.error(error, { allCookies });

        // Try to restore from localStorage
        const savedData = localStorage.getItem("interviewData");
        if (savedData) {
          Cookies.set("CreateVRres", savedData);
          createVRResJson = savedData;
        } else {
          toast("Interview data missing - please start over", {
            icon: '❌',
            description: "Could not find required data"
          });
          setError(error);
          setIsLoading(false);
          return;
        }
      }

      // Save to localStorage as backup
      localStorage.setItem("interviewData", createVRResJson);

      // Parse and validate cookie data
      let parsedData;
      try {
        parsedData = JSON.parse(createVRResJson);
        
        if (!parsedData || typeof parsedData !== 'object') {
          throw new Error("Invalid data structure");
        }

        // Validate required fields
        const requiredFields = ['candId', 'jobRole']; // Add any other required fields
        const missingFields = requiredFields.filter(field => !parsedData[field]);
        
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

      } catch (err) {
        const error = `Invalid interview data: ${err.message}`;
        console.error(error, { createVRResJson, parsedData });
        toast("Invalid interview setup - please start over", {
          icon: '❌',
          description: err.message
        });
        setError(error);
        setIsLoading(false);
        return;
      }

      // Log all possible role fields

      // Validate and extract role
      // Extract role with jobRole being the primary field
      let role = parsedData.jobRole || "Software Developer";
      
      // Try other fields if jobRole is not available
      if (role === "Software Developer") {
        const possibleRoleFields = ['role', 'position_name', 'position', 'job_role', 'title'];
        for (const field of possibleRoleFields) {
          if (parsedData[field] && typeof parsedData[field] === 'string' && parsedData[field].trim()) {
            role = parsedData[field].trim();
            break;
          }
        }
      }
      

      // Validate and extract skills
      let skills = ["general"]; // Default skills
      const possibleSkillFields = ['skills', 'technical_skills', 'required_skills', 'technologies'];
      for (const field of possibleSkillFields) {
        if (parsedData[field]) {
          const skillData = parsedData[field];
          if (Array.isArray(skillData)) {
            skills = skillData.filter(s => s && typeof s === 'string' && s.trim());
          } else if (typeof skillData === 'string' && skillData.trim()) {
            skills = [skillData.trim()];
          }
          if (skills.length > 0) break;
        }
      }


      // Use first skill as primary if available
      const primarySkill = skills[0] || "general";

      // Get first question from API
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: role,
          skills: skills,
          primarySkill: primarySkill,
          previousQuestions: [] // Empty array for first question
        })
      });

      let firstQuestion;
      
      try {
        // For the first question, always use the experience question regardless of API response
        const skill = skills[0] || 'JavaScript';

        firstQuestion = {
          question: `What is your experience with ${skill}?`,
          type: 'technical',
          keywords: ['experience', skill],
          difficulty: 'medium',
          startTimestamp: null, // Don't start timer until interview is active
          endTimestamp: null,
          answer: null,
          follow_up: "Please provide specific examples of projects or challenges where you've used this skill.",
          scores: {
            communication: 0,
            technical: 0,
            overall: 0
          }
        };

        console.log('📚 Questions data loaded (waiting for interview to start)');

        // We ignore the API response for the first question
        await response.json(); // consume response but don't use it
      } catch (error) {
        console.warn("Using default first question:", error);
        // Use primarySkill from the validated data above
        
        if (error.message === "Failed to fetch" || !navigator.onLine) {
          toast("Network error - using default question", { icon: '🌐' });
        } else {
          toast("Using default question", { icon: '⚠️' });
        }

        const defaultQ = getDefaultQuestion(role, skills[0] || 'JavaScript', 'technical');
        firstQuestion = {
          ...defaultQ,
          question: defaultQ.question.replace('[skill]', skills[0] || 'JavaScript'),
          startTimestamp: null,
          endTimestamp: null,
          answer: null
        };
        toast('Using default first question', { icon: '👋' });
      }

      // Clear any existing questions and start fresh
      localStorage.removeItem("questions");
      localStorage.removeItem("currentQuestionIndex");

      // Load questions but don't activate them yet
      const initialQuestions = [firstQuestion];
      setAllQuestions(initialQuestions);
      setQuestionsLoaded(true); // Mark questions as loaded
      setCurrentQuestion(null); // Don't set current question until activated
      setCurrentIndex(0);
      setError(null);

      // Save initial state but don't set current question yet
      localStorage.setItem("questions", JSON.stringify(initialQuestions));
      localStorage.setItem("currentQuestionIndex", "0");

      console.log('📚 Questions loaded successfully, waiting for interview activation');
    } catch (err) {
      console.error("Error in loadQuestionsData:", err);
      // Create experience question as fallback
      const skill = 'JavaScript'; // Default skill for fallback
      const fallbackQuestion = {
        question: `What is your experience with ${skill}?`,
        type: 'technical',
        keywords: ['experience', skill],
        difficulty: 'medium',
        startTimestamp: null,
        endTimestamp: null,
        answer: null, // No existing answer for fallback
        follow_up: "Please provide specific examples of projects or challenges where you've used this skill.",
        scores: {
          communication: 0,
          technical: 0,
          overall: 0
        }
      };
      setAllQuestions([fallbackQuestion]);
      setQuestionsLoaded(true); // Mark as loaded even for fallback
      setCurrentQuestion(null); // Don't set current question until activated
      setCurrentIndex(0);
      setError(err.message || "Failed to load interview questions");
      toast("Using backup question due to error", { icon: '⚠️' });

      // Save fallback state to localStorage
      localStorage.setItem("questions", JSON.stringify([fallbackQuestion]));
      localStorage.setItem("currentQuestionIndex", "0");
    } finally {
      setIsLoading(false);
    }
  };

  // Add function to activate questions when interview starts
  const activateQuestions = useCallback(() => {
    console.log('🎬 Attempting to activate questions:', {
      questionsLoaded,
      questionsCount: allQuestions.length,
      firstQuestion: allQuestions[0]?.question
    });

    if (!questionsLoaded) {
      console.error('Cannot activate questions - questionsLoaded is false');
      toast.error('Questions not loaded yet. Please wait and try again.');
      return false;
    }

    if (allQuestions.length === 0) {
      console.error('Cannot activate questions - no questions in array');
      toast.error('No questions available. Please refresh and try again.');
      return false;
    }

    console.log('🎬 Activating questions for interview start');

    // Set the current question and start timestamp
    const firstQuestion = {
      ...allQuestions[0],
      startTimestamp: new Date().toISOString()
    };

    const updatedQuestions = [firstQuestion, ...allQuestions.slice(1)];
    setAllQuestions(updatedQuestions);
    setCurrentQuestion(firstQuestion);
    setCurrentIndex(0);

    // Update localStorage
    localStorage.setItem("questions", JSON.stringify(updatedQuestions));
    localStorage.setItem("currentQuestionIndex", "0");

    console.log('✅ Questions activated successfully:', {
      firstQuestion: firstQuestion.question,
      timestamp: firstQuestion.startTimestamp
    });

    toast('Interview started - tell us about your experience', { icon: '🎬' });
    return true;
  }, [questionsLoaded, allQuestions]);

  // Remove automatic loading - questions will be loaded manually
  // useEffect(() => {
  //   loadFirstQuestion();
  // }, []);

  const startAnswering = () => {
    if (currentQuestion && !currentQuestion.startTimestamp) {
      const updatedQuestions = [...allQuestions];
      updatedQuestions[currentIndex] = {
        ...currentQuestion,
        startTimestamp: new Date().toISOString(),
        endTimestamp: null
      };
      setAllQuestions(updatedQuestions);
      setCurrentQuestion(updatedQuestions[currentIndex]);
    }
  };

  const nextQuestion = async () => {
    window.speechSynthesis.cancel();
    
    try {
      setError(null);
      setIsLoading(true);
      
      // Save current answer before proceeding
      if (currentQuestion && currentIndex >= 0) {
        const updatedQuestions = [...allQuestions];
        const currentAnswer = currentQuestion.answer;
        
        // Preserve the current answer exactly as is
        if (currentAnswer !== null && currentAnswer !== undefined) {
          updatedQuestions[currentIndex] = {
            ...currentQuestion,
            answer: currentAnswer
          };
          setAllQuestions(updatedQuestions);
          
          // Update localStorage with preserved answer
          localStorage.setItem("questions", JSON.stringify(updatedQuestions));
        }
      }

      // Get interview data
      // Try to get interview data with fallback to localStorage
      let createVRResJson = Cookies.get("CreateVRres");
      if (!createVRResJson) {
        const savedData = localStorage.getItem("interviewData");
        if (savedData) {
          Cookies.set("CreateVRres", savedData);
          createVRResJson = savedData;
        } else {
          const error = "Session data lost";
          console.error(error, { allCookies: Cookies.get() });
          toast("Session data lost - please restart", {
            icon: '❌',
            description: "Could not recover interview data"
          });
          setError(error);
          setIsLoading(false);
          return;
        }
      }

      // Keep localStorage in sync
      localStorage.setItem("interviewData", createVRResJson);

      // Parse and validate data again
      let parsedData;
      try {
        parsedData = JSON.parse(createVRResJson);
        
        if (!parsedData || typeof parsedData !== 'object') {
          throw new Error("Invalid data structure");
        }

        // Revalidate required fields
        const requiredFields = ['candId', 'jobRole'];
        const missingFields = requiredFields.filter(field => !parsedData[field]);
        
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

      } catch (err) {
        const error = `Interview data corrupted: ${err.message}`;
        console.error(error, { createVRResJson });
        toast("Interview data error - please restart", {
          icon: '❌',
          description: err.message
        });
        setError(error);
        setIsLoading(false);
        return;
      }


      // Validate and extract role (same as loadFirstQuestion)
      let role = "Software Developer";
      const possibleRoleFields = ['role', 'position_name', 'position', 'job_role', 'title'];
      for (const field of possibleRoleFields) {
        if (parsedData[field] && typeof parsedData[field] === 'string' && parsedData[field].trim()) {
          role = parsedData[field].trim();
          break;
        }
      }

      // Validate and extract skills (same as loadFirstQuestion)
      let skills = ["general"];
      const possibleSkillFields = ['skills', 'technical_skills', 'required_skills', 'technologies'];
      for (const field of possibleSkillFields) {
        if (parsedData[field]) {
          const skillData = parsedData[field];
          if (Array.isArray(skillData)) {
            skills = skillData.filter(s => s && typeof s === 'string' && s.trim());
          } else if (typeof skillData === 'string' && skillData.trim()) {
            skills = [skillData.trim()];
          }
          if (skills.length > 0) break;
        }
      }

      const primarySkill = skills[0] || "general";


      // Determine next question type
      const previousType = currentQuestion?.type || 'behavioral';
      const nextType = previousType === 'technical' ? 'behavioral' : 'technical';

      // Collect previous Q&A history and validate answers
      const previousQA = allQuestions.map(q => {
        // Clean and validate answer
        let answer = q.answer;
        if (typeof answer === 'string') {
          answer = answer.trim();
        }
        
        return {
          question: q.question,
          answer: answer, // Pass through answer without modification
          type: q.type || 'technical'
        };
      });

      // Get next question from API with conversation history
      const response = await fetch(`${import.meta.env.VITE_APP_HOST}/api/v1/generate-question`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          role: role,
          skills: skills,
          primarySkill: primarySkill,
          preferredType: nextType,
          previousQuestions: previousQA
        })
      });

      let nextQuestionData;
      try {
        if (!response.ok) {
          throw new Error('Failed to fetch next question');
        }

        const data = await response.json();

        if (data.success && data.question) {
          const questionText = typeof data.question.question === 'string'
            ? data.question.question
            : typeof data.question === 'string'
              ? data.question
              : null;

          if (!questionText) {
            throw new Error("Invalid question format");
          }

          // Check if question contains [skill] placeholder and replace it
          let formattedQuestion = questionText;
          if (formattedQuestion.includes('[skill]')) {
            formattedQuestion = formattedQuestion.replace('[skill]', skills[0] || 'JavaScript');
          }

          nextQuestionData = {
            question: formattedQuestion,
            type: nextType,
            keywords: Array.isArray(data.question.keywords) ? data.question.keywords : [],
            difficulty: data.question.difficulty || 'medium',
            startTimestamp: null,
            endTimestamp: null,
            answer: null,
            follow_up: data.question.follow_up || null
          };
        }
        else if (data.fallback) {
          toast('Using fallback question', { icon: '⚠️' });
          
          // Format fallback question if it has [skill] placeholder
          let fallbackQuestion = data.fallback;
          if (fallbackQuestion.includes('[skill]')) {
            fallbackQuestion = fallbackQuestion.replace('[skill]', skills[0] || 'JavaScript');
          }
          
          nextQuestionData = {
            question: fallbackQuestion,
            type: nextType,
            keywords: [],
            difficulty: 'medium',
            startTimestamp: null,
            endTimestamp: null,
            answer: null,
            follow_up: null
          };
        }
        else {
          throw new Error("Invalid response format");
        }
      } catch (error) {
        console.warn("API request failed:", error);
        
        const defaultQuestion = getDefaultQuestion(role, primarySkill, nextType);
        toast(
          error.message.includes("Failed to fetch")
            ? "Network error - using default question"
            : "API error - using default question",
          { icon: '⚠️' }
        );
        
        nextQuestionData = {
          ...defaultQuestion,
          startTimestamp: null,
          endTimestamp: null,
          answer: null,
          follow_up: null
        };
      }

      // Update current question and add new one
      const updatedQuestions = [...allQuestions];
      if (currentQuestion) {
        updatedQuestions[currentIndex] = {
          ...currentQuestion,
          endTimestamp: new Date().toISOString()
        };
      }

      updatedQuestions.push(nextQuestionData);
      setAllQuestions(updatedQuestions);
      if (currentIndex + 1 >= updatedQuestions.length) {
  // No more questions available
  return true; // Signal that we've reached the end
}
setCurrentIndex(currentIndex + 1);
      setCurrentIndex(currentIndex + 1);
      setCurrentQuestion(nextQuestionData);
      toast(`Next question`, { icon: '➡️' });
      return false; // Return false unless we hit MAX_QUESTIONS
      
    } catch (error) {
      console.error("Error getting next question:", error);
      
      // Create fallback question for next question
      // Use default question with proper skill replacement
      const defaultQ = getDefaultQuestion(role || "Software Developer", skills[0] || "JavaScript", nextType);
      const fallbackQuestion = {
        ...defaultQ,
        question: defaultQ.question.replace('[skill]', skills[0] || 'JavaScript'),
        startTimestamp: null,
        endTimestamp: null,
        answer: null,
        follow_up: null
      };

      // Update questions with fallback
      const updatedQuestions = [...allQuestions];
      if (currentQuestion) {
        updatedQuestions[currentIndex] = {
          ...currentQuestion,
          endTimestamp: new Date().toISOString()
        };
      }
      updatedQuestions.push(fallbackQuestion);
      
      // Update state with fallback
      setAllQuestions(updatedQuestions);
      setCurrentIndex(currentIndex + 1);
      setCurrentQuestion(fallbackQuestion);
      
      // Save to localStorage
      localStorage.setItem("questions", JSON.stringify(updatedQuestions));
      localStorage.setItem("currentQuestionIndex", (currentIndex + 1).toString());
      
      setError(error.message || "Failed to get next question - using fallback");
      toast("Using backup question due to error", { icon: '⚠️' });
      return false; // Always allow more questions
    } finally {
      setIsLoading(false);
    }
  };

  const calculateScores = (answer, question) => {
    // Handle empty or minimal answers
    if (!answer || answer === '-' || answer.trim().length === 0) {
      return {
        communication: 1, // Minimum score for no communication
        technical: 1,    // Minimum score for no technical content
        overall: 1       // Minimum overall score
      };
    }

    // Calculate communication score based on answer completeness
    let communicationScore = 3; // Start with neutral score
    const wordCount = answer.trim().split(/\s+/).length;
    
    if (wordCount < 5) {
      communicationScore = 1.5; // Very short answer
    } else if (wordCount < 15) {
      communicationScore = 2.5; // Short but present answer
    } else if (wordCount > 50) {
      communicationScore = 4.5; // Detailed response
    }

    // Calculate technical score based on content
    let technicalScore = 3; // Start with neutral score
    
    // Check for key indicators of answer quality
    const hasSpecificExamples = answer.toLowerCase().includes('example') ||
                               answer.toLowerCase().includes('project') ||
                               answer.includes('.');
    const hasDetailedExplanation = wordCount > 30 && answer.includes(',');
    const hasTechnicalTerms = answer.match(/\b(function|api|database|code|test|debug|deploy|develop)\b/i);
    
    if (hasSpecificExamples && hasDetailedExplanation && hasTechnicalTerms) {
      technicalScore = 5;
    } else if ((hasSpecificExamples && hasDetailedExplanation) ||
               (hasDetailedExplanation && hasTechnicalTerms) ||
               (hasSpecificExamples && hasTechnicalTerms)) {
      technicalScore = 4;
    } else if (hasSpecificExamples || hasDetailedExplanation || hasTechnicalTerms) {
      technicalScore = 3.5;
    } else if (wordCount < 10) {
      technicalScore = 2;
    }

    // Calculate overall score with weighted average
    const overallScore = (communicationScore * 0.4 + technicalScore * 0.6);

    return {
      communication: Number(communicationScore.toFixed(1)),
      technical: Number(technicalScore.toFixed(1)),
      overall: Number(overallScore.toFixed(1))
    };
  };

  const updateAnswer = useCallback(async (value) => {
    if (!currentQuestion || currentIndex < 0) {
      console.warn("Invalid state for answer update:", {
        hasCurrentQuestion: !!currentQuestion,
        currentIndex
      });
      return;
    }

    try {
      // Only update if the value is different from current answer
      if (currentQuestion.answer !== value) {
        // Only log when an answer is finalized (from AnswerInput submit or audio recording completion)
        // if (value && value !== currentQuestion.answer && !value.endsWith('...')) {
        //   console.log("Answer submitted:", {
        //     questionIndex: currentIndex,
        //     question: currentQuestion.question,
        //     finalAnswer: value
        //   });
        // }

        // Calculate scores for the answer
        const scores = calculateScores(value, currentQuestion);

        const updatedQuestions = [...allQuestions];
        const updatedQuestion = {
          ...currentQuestion,
          answer: value,
          lastModified: new Date().toISOString(),
          endTimestamp: new Date().toISOString(),
          scores: scores
        };
        updatedQuestions[currentIndex] = updatedQuestion;

        // Update state
        setAllQuestions(updatedQuestions);
        setCurrentQuestion(updatedQuestion);

        // Immediately save to localStorage to prevent loss
        localStorage.setItem("questions", JSON.stringify(updatedQuestions));
      }
    } catch (error) {
      console.error("Error updating answer:", error);
      toast.error("Failed to save answer. Please try again.");
    }
  }, [currentQuestion, currentIndex, allQuestions]);

  // Add an effect to keep localStorage in sync with state changes
  useEffect(() => {
    if (allQuestions.length > 0) {
      localStorage.setItem("questions", JSON.stringify(allQuestions));
    }
  }, [allQuestions]);

  // Initialize questions if they exist in localStorage
  useEffect(() => {
    const savedQuestions = localStorage.getItem("questions");
    if (savedQuestions) {
      try {
        const parsed = JSON.parse(savedQuestions);
        if (Array.isArray(parsed) && parsed.length > 0) {
          setAllQuestions(parsed);
        }
      } catch (error) {
        console.error("Error parsing saved questions:", error);
      }
    }
  }, []);

  const initializeQuestions = async (initialQuestions) => {
    try {
      console.log('📚 Initializing questions (loading without activation):', initialQuestions);

      if (!Array.isArray(initialQuestions) || initialQuestions.length === 0) {
        throw new Error('Invalid or empty questions array');
      }

      // Clear existing data
      localStorage.removeItem("questions");
      localStorage.removeItem("currentQuestionIndex");

      // Normalize questions but don't activate them
      const normalizedQuestions = initialQuestions.map((q) => ({
        ...q,
        question: q.question || (typeof q === 'string' ? q : '') || "What is your experience with [skill]?",
        startTimestamp: null, // Don't start timer until interview is active
        endTimestamp: null,
        answer: null,
        follow_up: q.follow_up || null,
        type: q.type || 'technical',
        scores: {
          communication: 0,
          technical: 0,
          overall: 0
        }
      }));

      // Update state but don't set current question yet
      setAllQuestions(normalizedQuestions);
      setQuestionsLoaded(true); // Mark questions as loaded
      setCurrentQuestion(null); // Don't set current question until activated
      setCurrentIndex(0);
      setError(null);

      // Save to localStorage
      localStorage.setItem("questions", JSON.stringify(normalizedQuestions));
      localStorage.setItem("currentQuestionIndex", "0");

      console.log('✅ Questions loaded successfully, ready for activation');
      return true;
    } catch (error) {
      console.error("Error initializing questions:", error);
      setError("Failed to initialize questions");
      setQuestionsLoaded(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    questions: allQuestions,
    currentQuestion,
    currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    initializeQuestions,
    loadQuestionsData, // New function to load questions without activating
    activateQuestions, // New function to activate questions when interview starts
    questionsLoaded,   // New state to track if questions are loaded
    error,
    isLoading
  };
}
